@echo off
chcp 65001 >nul
echo ========================================
echo HTTPS Proxy Test Script
echo HTTPS代理测试脚本
echo ========================================
echo.

echo Please ensure the server is started with proxy features enabled:
echo 请确保服务器已启动并启用了代理功能：
echo ./sweb.exe -proxy
echo.

echo Testing HTTPS proxy functionality...
echo 测试HTTPS代理功能...
echo.

echo 1. Compiling test program...
echo 1. 编译测试程序...
go build -o test_https_proxy.exe test_proxy.go
if %errorlevel% neq 0 (
    echo X Compilation failed
    echo X 编译失败
    pause
    exit /b 1
)
echo √ Compilation successful
echo √ 编译成功

echo.
echo 2. Running HTTPS proxy test...
echo 2. 运行HTTPS代理测试...
test_https_proxy.exe

echo.
echo ========================================
echo HTTPS proxy test completed!
echo HTTPS代理测试完成！
echo ========================================
echo.
echo Browser configuration for HTTPS proxy:
echo 浏览器HTTPS代理配置：
echo 1. HTTP Proxy: 127.0.0.1:10808
echo 1. HTTP代理: 127.0.0.1:10808
echo 2. Enable proxy for HTTPS
echo 2. 为HTTPS启用代理
echo 3. Test with: https://www.baidu.com
echo 3. 测试网址: https://www.baidu.com
echo.
pause
