@echo off
chcp 65001 >nul
echo ========================================
echo SWeb Proxy Function Test Script
echo SWeb 代理功能测试脚本
echo ========================================
echo.

echo Please ensure the server is started with proxy features enabled:
echo 请确保服务器已启动并启用了代理功能：
echo ./sweb.exe -socks5 -proxy
echo.

echo 1. Testing comprehensive function status...
echo 1. 测试综合功能状态...
go run test_all.go
echo.

echo 2. Testing SOCKS5 proxy function...
echo 2. 测试SOCKS5代理功能...
go run test_socks5.go
echo.

echo 3. Testing HTTP proxy function...
echo 3. 测试HTTP代理功能...
go run test_proxy.go
echo.

echo ========================================
echo Test completed!
echo 测试完成！
echo ========================================
pause
