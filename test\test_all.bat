@echo off
chcp 65001 >nul 2>&1
echo ========================================
echo SWeb Complete Test Suite
echo SWeb 完整测试套件
echo ========================================
echo.

echo Please ensure the server is started with all features enabled:
echo 请确保服务器已启动并启用了所有功能：
echo ./sweb.exe -upload -files -webdav -https -socks5 -proxy
echo.

echo Starting comprehensive tests...
echo 开始综合测试...
echo.

:menu
echo ========================================
echo Test Menu / 测试菜单
echo ========================================
echo 1. Run all tests / 运行所有测试
echo 2. Basic functionality test / 基础功能测试
echo 3. SOCKS5 proxy test / SOCKS5代理测试
echo 4. HTTP/HTTPS proxy test / HTTP/HTTPS代理测试
echo 5. Debug connection test / 调试连接测试
echo 6. Quick SOCKS5 test / SOCKS5快速测试
echo 0. Exit / 退出
echo ========================================
set /p choice="Please select an option / 请选择选项 (0-6): "

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :all_tests
if "%choice%"=="2" goto :basic_test
if "%choice%"=="3" goto :socks5_test
if "%choice%"=="4" goto :proxy_test
if "%choice%"=="5" goto :debug_test
if "%choice%"=="6" goto :quick_test

echo Invalid option / 无效选项
goto :menu

:all_tests
echo ========================================
echo Running All Tests / 运行所有测试
echo ========================================
call :basic_test
call :socks5_test
call :proxy_test
call :debug_test
goto :menu

:basic_test
echo.
echo ========================================
echo 1. Basic Functionality Test
echo 1. 基础功能测试
echo ========================================
echo.
echo Testing server status and basic features...
echo 测试服务器状态和基础功能...
go run test_all.go
echo.
pause
goto :menu

:socks5_test
echo.
echo ========================================
echo 2. SOCKS5 Proxy Test
echo 2. SOCKS5代理测试
echo ========================================
echo.
echo Compiling SOCKS5 test...
echo 编译SOCKS5测试...
go build -o test_socks5_temp.exe test_socks5.go
if %errorlevel% neq 0 (
    echo X SOCKS5 test compilation failed
    echo X SOCKS5测试编译失败
    pause
    goto :menu
)

echo Running SOCKS5 proxy test...
echo 运行SOCKS5代理测试...
test_socks5_temp.exe
if exist test_socks5_temp.exe del test_socks5_temp.exe
echo.
pause
goto :menu

:proxy_test
echo.
echo ========================================
echo 3. HTTP/HTTPS Proxy Test
echo 3. HTTP/HTTPS代理测试
echo ========================================
echo.
echo Compiling HTTP proxy test...
echo 编译HTTP代理测试...
go build -o test_proxy_temp.exe test_proxy.go
if %errorlevel% neq 0 (
    echo X HTTP proxy test compilation failed
    echo X HTTP代理测试编译失败
    pause
    goto :menu
)

echo Running HTTP/HTTPS proxy test...
echo 运行HTTP/HTTPS代理测试...
test_proxy_temp.exe
if exist test_proxy_temp.exe del test_proxy_temp.exe
echo.
pause
goto :menu

:debug_test
echo.
echo ========================================
echo 4. Debug Connection Test
echo 4. 调试连接测试
echo ========================================
echo.
echo Compiling debug test...
echo 编译调试测试...
go build -o debug_socks5_temp.exe debug_socks5.go
if %errorlevel% neq 0 (
    echo X Debug test compilation failed
    echo X 调试测试编译失败
    pause
    goto :menu
)

echo Running debug connection test...
echo 运行调试连接测试...
debug_socks5_temp.exe
if exist debug_socks5_temp.exe del debug_socks5_temp.exe
echo.
pause
goto :menu

:quick_test
echo.
echo ========================================
echo 5. Quick SOCKS5 Test
echo 5. SOCKS5快速测试
echo ========================================
echo.
echo Compiling main program...
echo 编译主程序...
cd ..
go build -o sweb_temp.exe main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go
if %errorlevel% neq 0 (
    echo X Main program compilation failed
    echo X 主程序编译失败
    cd test
    pause
    goto :menu
)
cd test

echo √ Compilation successful
echo √ 编译成功
echo.
echo Please run in another command window: ../sweb_temp.exe -socks5
echo 请在另一个命令行窗口运行: ../sweb_temp.exe -socks5
echo Then press any key to continue testing...
echo 然后按任意键继续测试...
pause

echo.
echo Testing SOCKS5 connection...
echo 测试SOCKS5连接...
go run debug_socks5.go

if exist ..\sweb_temp.exe del ..\sweb_temp.exe
echo.
pause
goto :menu

:end
echo.
echo ========================================
echo Test Suite Completed!
echo 测试套件完成！
echo ========================================
echo.
echo Summary / 总结:
echo - Server status: http://localhost:8080/api/status
echo - 服务器状态: http://localhost:8080/api/status
echo - SOCKS5 proxy: localhost:1080
echo - SOCKS5代理: localhost:1080
echo - HTTP proxy: localhost:10808
echo - HTTP代理: localhost:10808
echo.
echo Browser configuration tips:
echo 浏览器配置提示：
echo 1. For SOCKS5: Set SOCKS5 proxy to 127.0.0.1:1080
echo 1. SOCKS5: 设置SOCKS5代理为 127.0.0.1:1080
echo 2. For HTTP/HTTPS: Set HTTP proxy to 127.0.0.1:10808
echo 2. HTTP/HTTPS: 设置HTTP代理为 127.0.0.1:10808
echo.
echo Thank you for using SWeb!
echo 感谢使用 SWeb！
echo.
pause
