# 浏览器SOCKS5代理配置说明

## 问题诊断

您遇到的错误 "SOCKS5认证失败: 无效的SOCKS5版本" 通常是由以下原因造成的：

1. **浏览器发送了错误的协议数据**
2. **代理设置配置错误**
3. **网络连接问题**

## 修复措施

我已经对SOCKS5代理服务器进行了以下改进：

1. **增强了错误处理和调试信息**
2. **改进了协议版本检查**
3. **添加了详细的日志输出**

## 重新测试步骤

### 1. 重新编译和启动服务器

```bash
# 重新编译
go build -o sweb.exe main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go

# 启动服务器（带详细日志）
./sweb.exe -socks5 -proxy
```

### 2. 浏览器配置检查

#### Chrome/Edge配置
1. 打开设置 → 高级 → 系统 → 打开代理设置
2. 手动代理配置
3. **重要**: 确保配置为SOCKS5代理，不是HTTP代理
   - SOCKS主机: `127.0.0.1` 或 `localhost`
   - 端口: `1080`
   - 协议类型: **SOCKS5** (不是SOCKS4)

#### Firefox配置
1. 设置 → 网络设置 → 手动代理配置
2. SOCKS主机: `127.0.0.1`
3. 端口: `1080`
4. **选择SOCKS v5** (不是SOCKS v4)
5. 勾选"通过SOCKS代理DNS查询"

### 3. 使用调试工具测试

运行调试工具来验证SOCKS5连接：

```bash
go run debug_socks5.go
```

这个工具会：
- 测试与SOCKS5代理的连接
- 显示详细的协议交互过程
- 帮助定位具体问题

### 4. 查看服务器日志

启动服务器后，观察控制台输出。现在会显示详细的调试信息：
- 接收到的数据内容
- 协议版本检查结果
- 认证方法协商过程

## 常见配置错误

### ❌ 错误配置
```
HTTP代理: 127.0.0.1:1080  # 错误：这是HTTP代理设置
SOCKS4代理: 127.0.0.1:1080  # 错误：版本不对
```

### ✅ 正确配置
```
SOCKS5代理: 127.0.0.1:1080  # 正确：SOCKS5协议
```

## 替代测试方法

如果浏览器配置仍有问题，可以使用命令行工具测试：

### 使用curl测试
```bash
# 通过SOCKS5代理访问网站
curl --socks5 127.0.0.1:1080 http://www.baidu.com
```

### 使用proxychains (Linux)
```bash
# 配置proxychains
echo "socks5 127.0.0.1 1080" >> /etc/proxychains.conf

# 通过代理运行命令
proxychains curl http://www.baidu.com
```

## 故障排除步骤

1. **确认服务器启动**
   ```bash
   netstat -an | grep :1080
   ```

2. **测试基本连接**
   ```bash
   telnet 127.0.0.1 1080
   ```

3. **运行调试工具**
   ```bash
   go run debug_socks5.go
   ```

4. **检查防火墙设置**
   - Windows: 检查Windows防火墙
   - 杀毒软件: 检查是否阻止了连接

5. **尝试不同端口**
   ```bash
   ./sweb.exe -socks5 -socks5-port 1081
   ```

## 如果问题仍然存在

请提供以下信息：
1. 服务器启动时的完整日志输出
2. 浏览器的具体型号和版本
3. 操作系统版本
4. 运行 `go run debug_socks5.go` 的输出结果

这将帮助进一步诊断问题。
