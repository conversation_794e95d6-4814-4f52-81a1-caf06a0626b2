# SWeb 项目结构说明

## 目录结构

```
sweb/
├── main.go                     # 主程序入口文件
├── upload.go                   # 文件上传功能模块
├── files.go                    # 文件浏览功能模块
├── webdav.go                   # WebDAV服务模块
├── server.go                   # HTTP/HTTPS服务器模块
├── utils.go                    # 工具函数和页面生成
├── socks5.go                   # SOCKS5代理服务器模块
├── proxy.go                    # HTTP代理服务器模块
├── go.mod                      # Go模块文件
├── go.sum                      # 依赖校验文件
├── Makefile                    # 构建脚本（Linux/macOS）
├── build.bat                   # 构建脚本（Windows）
├── README.md                   # 项目说明
├── 项目结构说明.md             # 本文件
├── HTTPS代理配置指南.md        # HTTPS代理配置指南
├── HTTPS功能说明.md            # HTTPS功能说明
├── WebDAV使用说明.md           # WebDAV使用说明
├── 测试说明.md                 # 测试说明文档
├── 浏览器配置说明.md           # 浏览器配置说明
├── bin/                        # 编译输出目录
│   ├── windows/                # Windows可执行文件
│   └── linux/                  # Linux可执行文件
├── cert/                       # SSL证书目录
│   ├── server.crt              # SSL证书文件
│   └── server.key              # SSL私钥文件
├── test/                       # 测试代码目录
│   ├── test_all.bat            # Windows完整测试套件
│   ├── test_all.sh             # Linux/macOS完整测试套件
│   ├── test_all.go             # 综合功能测试
│   ├── test_socks5.go          # SOCKS5代理测试
│   ├── test_proxy.go           # HTTP代理测试
│   └── debug_socks5.go         # SOCKS5调试工具
├── tools/                      # 工具目录
│   └── generate-cert.go        # SSL证书生成工具
└── web/                        # Web文件目录
    └── index.html              # 默认主页
```

## 模块说明

### 核心模块

#### main.go
- 程序入口点
- 命令行参数解析
- 功能模块初始化和启动
- 全局配置管理

#### upload.go
- 文件上传功能实现
- 多文件上传支持
- 上传进度显示
- 文件大小限制

#### files.go
- 文件浏览功能实现
- 目录遍历和文件列表
- 文件下载支持
- 文件信息展示

#### webdav.go
- WebDAV协议实现
- 文件和目录操作
- 读写权限控制
- 标准WebDAV方法支持

#### server.go
- HTTP/HTTPS服务器实现
- SSL证书管理
- 服务器启动和停止
- 端口配置管理

#### utils.go
- 工具函数集合
- 页面生成和模板
- 状态API实现
- 帮助信息显示

#### socks5.go
- SOCKS5代理服务器实现
- 协议握手和认证
- 连接转发和数据传输
- 统计信息收集

#### proxy.go
- HTTP/HTTPS代理服务器实现
- HTTP CONNECT方法支持
- SSL隧道建立
- 请求转发和响应处理

### 测试模块

#### test/test_all.bat & test/test_all.sh
- 完整的测试套件
- 交互式菜单界面
- 支持单独测试各个功能
- 中英文双语支持

#### test/test_all.go
- 综合功能测试
- 服务器状态检查
- API端点验证
- 基础连接测试

#### test/test_socks5.go
- SOCKS5代理专项测试
- 协议握手验证
- 数据传输测试
- 并发连接测试

#### test/test_proxy.go
- HTTP/HTTPS代理专项测试
- HTTP请求测试
- HTTPS CONNECT测试
- SSL隧道验证

#### test/debug_socks5.go
- SOCKS5连接调试工具
- 详细的协议交互显示
- 连接问题诊断
- 错误信息分析

### 构建工具

#### Makefile
- Linux/macOS构建脚本
- 支持多平台交叉编译
- 自动化构建流程
- 清理和安装目标

#### build.bat
- Windows构建脚本
- 批量编译支持
- 错误处理和状态显示
- 中文环境兼容

## 编译说明

### 使用Makefile（推荐）
```bash
# 编译所有平台
make all

# 编译当前平台
make build

# 编译Windows版本
make windows

# 编译Linux版本
make linux

# 清理编译文件
make clean
```

### 使用build.bat（Windows）
```cmd
# 编译Windows和Linux版本
build.bat
```

### 手动编译
```bash
# 编译当前平台
go build -o sweb main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go

# 交叉编译Windows
GOOS=windows GOARCH=amd64 go build -o sweb.exe main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go

# 交叉编译Linux
GOOS=linux GOARCH=amd64 go build -o sweb main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go
```

## 测试说明

### 运行完整测试套件

#### Windows
```cmd
cd test
test_all.bat
```

#### Linux/macOS
```bash
cd test
chmod +x test_all.sh
./test_all.sh
```

### 单独运行测试
```bash
cd test

# 基础功能测试
go run test_all.go

# SOCKS5代理测试
go run test_socks5.go

# HTTP代理测试
go run test_proxy.go

# 调试工具
go run debug_socks5.go
```

## 开发指南

### 添加新功能模块

1. **创建新的.go文件**
   - 文件名应该反映功能（如：新功能.go）
   - 保持在根目录下与其他模块一致

2. **更新构建脚本**
   - 在Makefile的SOURCE_FILES中添加新文件
   - 在build.bat的SOURCE_FILES中添加新文件

3. **集成到main.go**
   - 添加相应的命令行参数
   - 在main函数中初始化新功能

4. **添加测试**
   - 在test目录下创建测试文件
   - 更新test_all.bat和test_all.sh

### 代码规范

- 使用Go标准格式化工具：`go fmt`
- 添加适当的注释和文档
- 遵循Go命名约定
- 保持函数简洁和单一职责

### 调试技巧

- 使用详细的日志输出
- 利用debug_socks5.go等调试工具
- 检查服务器状态API：`/api/status`
- 使用浏览器开发者工具检查网络请求

## 部署说明

### 生产环境部署

1. **编译优化版本**
   ```bash
   go build -ldflags="-s -w" -o sweb main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go
   ```

2. **准备SSL证书**（如果使用HTTPS）
   ```bash
   mkdir cert
   # 将SSL证书文件放入cert目录
   ```

3. **创建web目录**
   ```bash
   mkdir web
   # 放入自定义的静态文件
   ```

4. **启动服务**
   ```bash
   # 基础服务
   ./sweb

   # 启用所有功能
   ./sweb -upload -files -webdav -https -socks5 -proxy
   ```

### 安全考虑

- 代理功能默认禁用，需明确启用
- 建议在可信网络环境中使用
- 定期检查访问日志
- 考虑添加访问控制和认证机制

## 故障排除

### 常见问题

1. **编译错误**
   - 检查Go版本（建议1.16+）
   - 确保所有源文件在同一目录
   - 运行`go mod tidy`更新依赖

2. **运行时错误**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 查看详细错误日志

3. **测试失败**
   - 确保服务器已启动
   - 检查网络连接
   - 使用调试工具诊断

更多详细信息请参考相关文档文件。
