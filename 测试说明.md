# SWeb 测试说明

## 新的目录结构

所有测试相关文件现在都位于 `test/` 目录下：

```
test/
├── test_all.bat            # Windows完整测试套件（合并版）
├── test_all.sh             # Linux/macOS完整测试套件（合并版）
├── test_all.go             # 综合功能测试
├── test_socks5.go          # SOCKS5代理专项测试
├── test_proxy.go           # HTTP代理专项测试
└── debug_socks5.go         # SOCKS5调试工具
```

## 测试文件说明

### 测试程序文件
- `test_all.go` - 综合功能测试，检查所有功能状态
- `test_socks5.go` - SOCKS5代理专项测试
- `test_proxy.go` - HTTP代理专项测试
- `debug_socks5.go` - SOCKS5连接调试工具

### 测试脚本（已合并）
- `test_all.bat` - Windows完整测试套件（支持中文，交互式菜单）
- `test_all.sh` - Linux/macOS完整测试套件（交互式菜单）

## 运行测试

### 1. 启动服务器
首先启动SWeb服务器并启用代理功能：

```bash
# 启用所有功能进行完整测试
./sweb.exe -upload -files -webdav -https -socks5 -proxy

# 或者只启用代理功能
./sweb.exe -socks5 -proxy
```

### 2. 运行测试

#### Windows
```cmd
# 进入测试目录
cd test

# 运行完整测试套件（推荐，交互式菜单）
test_all.bat

# 或者单独运行测试
go run test_all.go
go run test_socks5.go
go run test_proxy.go
go run debug_socks5.go
```

**注意**:
- 测试脚本已针对Windows中文版进行优化，使用UTF-8编码避免乱码问题
- 新的测试脚本提供交互式菜单，可以选择运行特定测试
- 所有旧的测试脚本已合并到 `test_all.bat` 中

#### Linux/macOS
```bash
# 进入测试目录
cd test

# 给脚本执行权限
chmod +x test_all.sh

# 运行完整测试套件（交互式菜单）
./test_all.sh

# 或者单独运行测试
go run test_all.go
go run test_socks5.go
go run test_proxy.go
go run debug_socks5.go
```

## 测试内容

### 综合功能测试 (test_all.go)
- 检查服务器状态API
- 验证各功能模块启用状态
- 基础连接测试

### SOCKS5代理测试 (test_socks5.go)
- 代理服务器连接测试
- SOCKS5协议握手测试
- HTTP请求通过SOCKS5代理测试
- 并发连接测试

### HTTP代理测试 (test_proxy.go)
- 代理服务器连接测试
- HTTP GET请求测试
- HTTPS CONNECT请求测试
- Go HTTP客户端代理测试
- **HTTPS代理功能测试** (新增)
  - 测试多个HTTPS网站访问
  - 验证SSL/TLS隧道功能
  - 检查HTTPS响应完整性
- 并发请求测试

## 测试要求

### 依赖包
测试程序需要以下Go包：
```bash
go get golang.org/x/net/proxy
```

### 网络要求
- 测试程序会尝试访问外部网站（如httpbin.org、baidu.com）
- 如果网络受限，某些测试可能失败，但不影响代理功能本身

### 端口要求
- HTTP服务器：8080
- SOCKS5代理：1080
- HTTP代理：10808

确保这些端口未被其他程序占用。

## 故障排除

### 常见问题

1. **Windows中文版乱码问题**
   - 所有批处理文件已添加 `chcp 65001` 命令
   - 如仍有乱码，请在命令行手动执行 `chcp 65001`
   - 建议使用Windows Terminal或PowerShell

2. **连接被拒绝**
   - 确保服务器已启动
   - 检查端口是否被占用
   - 确认防火墙设置

3. **SOCKS5握手失败**
   - 确认使用了 `-socks5` 参数启动服务器
   - 检查SOCKS5端口配置
   - 运行 `quick_test.bat` 进行快速诊断

4. **HTTP代理测试失败**
   - 确认使用了 `-proxy` 参数启动服务器
   - 检查HTTP代理端口配置

5. **HTTPS代理无法访问**
   - 确保浏览器HTTP代理设置正确
   - HTTPS使用与HTTP相同的代理端口(10808)
   - 检查目标HTTPS网站是否可访问

6. **网络请求超时**
   - 检查网络连接
   - 尝试访问其他测试网站
   - 某些网站可能被防火墙阻止

### 调试建议
- 查看服务器控制台输出的日志信息
- 使用 `netstat -an | grep :端口号` 检查端口状态
- 逐个运行测试程序定位问题

## 性能测试

测试程序包含基础的并发测试，验证代理服务器的多连接处理能力。如需更详细的性能测试，可以：

1. 增加并发连接数
2. 测试长时间运行稳定性
3. 监控内存和CPU使用情况
4. 测试大文件传输性能

## 扩展测试

可以根据需要扩展测试内容：
- 添加更多目标网站测试
- 测试不同类型的网络协议
- 添加错误处理测试
- 测试边界条件和异常情况
