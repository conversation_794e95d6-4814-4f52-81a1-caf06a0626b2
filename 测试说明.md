# SWeb 代理功能测试说明

## 测试文件说明

### 测试程序文件
- `test_all.go` - 综合功能测试，检查所有功能状态
- `test_socks5.go` - SOCKS5代理专项测试
- `test_proxy.go` - HTTP代理专项测试

### 测试脚本
- `test.bat` - Windows批处理测试脚本
- `test.sh` - Linux/macOS Shell测试脚本

## 运行测试

### 1. 启动服务器
首先启动SWeb服务器并启用代理功能：

```bash
# 启用所有功能进行完整测试
./sweb.exe -upload -files -webdav -https -socks5 -proxy

# 或者只启用代理功能
./sweb.exe -socks5 -proxy
```

### 2. 运行测试

#### Windows
```cmd
# 运行完整测试套件
test.bat

# 或者单独运行测试
go run test_all.go
go run test_socks5.go
go run test_proxy.go
```

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x test.sh

# 运行完整测试套件
./test.sh

# 或者单独运行测试
go run test_all.go
go run test_socks5.go
go run test_proxy.go
```

## 测试内容

### 综合功能测试 (test_all.go)
- 检查服务器状态API
- 验证各功能模块启用状态
- 基础连接测试

### SOCKS5代理测试 (test_socks5.go)
- 代理服务器连接测试
- SOCKS5协议握手测试
- HTTP请求通过SOCKS5代理测试
- 并发连接测试

### HTTP代理测试 (test_proxy.go)
- 代理服务器连接测试
- HTTP GET请求测试
- HTTPS CONNECT请求测试
- Go HTTP客户端代理测试
- 并发请求测试

## 测试要求

### 依赖包
测试程序需要以下Go包：
```bash
go get golang.org/x/net/proxy
```

### 网络要求
- 测试程序会尝试访问外部网站（如httpbin.org、baidu.com）
- 如果网络受限，某些测试可能失败，但不影响代理功能本身

### 端口要求
- HTTP服务器：8080
- SOCKS5代理：1080
- HTTP代理：10808

确保这些端口未被其他程序占用。

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 确保服务器已启动
   - 检查端口是否被占用
   - 确认防火墙设置

2. **SOCKS5握手失败**
   - 确认使用了 `-socks5` 参数启动服务器
   - 检查SOCKS5端口配置

3. **HTTP代理测试失败**
   - 确认使用了 `-proxy` 参数启动服务器
   - 检查HTTP代理端口配置

4. **网络请求超时**
   - 检查网络连接
   - 尝试访问其他测试网站

### 调试建议
- 查看服务器控制台输出的日志信息
- 使用 `netstat -an | grep :端口号` 检查端口状态
- 逐个运行测试程序定位问题

## 性能测试

测试程序包含基础的并发测试，验证代理服务器的多连接处理能力。如需更详细的性能测试，可以：

1. 增加并发连接数
2. 测试长时间运行稳定性
3. 监控内存和CPU使用情况
4. 测试大文件传输性能

## 扩展测试

可以根据需要扩展测试内容：
- 添加更多目标网站测试
- 测试不同类型的网络协议
- 添加错误处理测试
- 测试边界条件和异常情况
