package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
)

// createDefaultPageIfNeeded 检查并创建默认页面
func createDefaultPageIfNeeded(webDir string, uploadEnabled bool) {
	// 检查是否存在默认页面
	indexFiles := []string{"index.html", "index.htm"}
	hasDefaultPage := false

	for _, indexFile := range indexFiles {
		indexPath := filepath.Join(webDir, indexFile)
		if _, err := os.Stat(indexPath); err == nil {
			hasDefaultPage = true
			break
		}
	}

	// 如果没有默认页面，创建一个
	if !hasDefaultPage {
		indexPath := filepath.Join(webDir, "index.html")
		indexContent := generateEnhancedDefaultPageContent()

		err := os.WriteFile(indexPath, []byte(indexContent), 0644)
		if err != nil {
			log.Printf("警告：无法创建默认页面: %v", err)
		} else {
			fmt.Println("已创建默认页面: index.html (支持上传和WebDAV状态检查)")
		}
	}
}

// statusHandler 处理状态查询请求
func statusHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	response := map[string]interface{}{
		"upload": map[string]interface{}{
			"enabled": uploadEnabled,
			"status": func() string {
				if uploadEnabled {
					return "enabled"
				}
				return "disabled"
			}(),
		},
		"files": map[string]interface{}{
			"enabled": filesEnabled,
			"status": func() string {
				if filesEnabled {
					return "enabled"
				}
				return "disabled"
			}(),
		},
		"webdav": map[string]interface{}{
			"enabled":   webdavEnabled,
			"readonly":  webdavReadonly,
			"directory": webdavDir,
			"status": func() string {
				if webdavEnabled {
					if webdavReadonly {
						return "enabled-readonly"
					}
					return "enabled-readwrite"
				}
				return "disabled"
			}(),
		},
		"https": map[string]interface{}{
			"enabled":   httpsEnabled,
			"httpPort":  httpPort,
			"httpsPort": httpsPort,
			"certDir":   certDir,
			"certStatus": func() string {
				if !httpsEnabled {
					return "disabled"
				}
				if err := checkCertificates(); err != nil {
					return "cert-error"
				}
				return "enabled"
			}(),
		},
	}

	json.NewEncoder(w).Encode(response)
}

// showHelpInfo 显示帮助信息
func showHelpInfo() {
	fmt.Println("简单Web文件服务器 - 基于Go语言开发")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  sweb.exe [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -upload, --enable-upload    启用文件上传功能 (默认: 禁用)")
	fmt.Println("  -files, --enable-files      启用文件浏览功能 (默认: 禁用)")
	fmt.Println("  -webdav, --enable-webdav    启用WebDAV服务 (默认: 禁用)")
	fmt.Println("  -webdav-dir <目录>          WebDAV服务的根目录 (默认: 当前目录)")
	fmt.Println("  -webdav-readonly            WebDAV服务只读模式 (默认: 读写)")
	fmt.Println("  -https, --enable-https      启用HTTPS服务 (默认: 禁用)")
	fmt.Println("  -port, -p <端口>           指定HTTP服务器端口 (默认: 8080)")
	fmt.Println("  -https-port <端口>         指定HTTPS服务器端口 (默认: 8443)")
	fmt.Println("  -cert-dir <目录>           SSL证书目录 (默认: ./cert)")
	fmt.Println("  -help, -h                  显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  sweb.exe                           # 启动HTTP服务器，仅提供静态文件服务")
	fmt.Println("  sweb.exe -upload                   # 启动服务器并启用文件上传功能")
	fmt.Println("  sweb.exe -files                    # 启动服务器并启用文件浏览功能")
	fmt.Println("  sweb.exe -https                    # 启动HTTP和HTTPS服务器")
	fmt.Println("  sweb.exe -webdav                   # 启动服务器并启用WebDAV服务")
	fmt.Println("  sweb.exe -webdav -webdav-readonly  # 启动只读WebDAV服务")
	fmt.Println("  sweb.exe -webdav -webdav-dir /data # 指定WebDAV目录")
	fmt.Println("  sweb.exe -upload -files -webdav -https # 启用所有功能")
	fmt.Println("  sweb.exe -https -https-port 9443   # 指定HTTPS端口")
	fmt.Println()
	fmt.Println("HTTPS证书:")
	fmt.Println("  证书文件: ./cert/server.crt")
	fmt.Println("  私钥文件: ./cert/server.key")
	fmt.Println("  可以使用openssl生成自签名证书用于测试")
	fmt.Println()
	fmt.Println("访问地址:")
	fmt.Println("  HTTP: http://localhost:8080")
	fmt.Println("  HTTPS: https://localhost:8443 (如果启用)")
	fmt.Println("  WebDAV: http://localhost:8080/webdav")
	fmt.Println()
	fmt.Println("安全说明:")
	fmt.Println("  文件上传、文件浏览、WebDAV和HTTPS功能默认禁用以确保服务器安全。")
	fmt.Println("  只有在明确需要时才使用相应参数启用。")
}

// generateEnhancedDefaultPageContent 生成包含WebDAV功能的增强版默认页面
func generateEnhancedDefaultPageContent() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Web文件服务器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
            border-radius: 5px;
        }
        .button {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        code {
            background: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .status-indicator {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status-enabled {
            color: #28a745;
        }
        .status-disabled {
            color: #dc3545;
        }
        .status-readonly {
            color: #ffc107;
        }
        .loading {
            color: #6c757d;
        }
        .hidden {
            display: none;
        }
        .webdav-info {
            background: #e7f3ff;
            border-left: 4px solid #007acc;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 简单Web文件服务器</h1>

        <p>欢迎使用这个简单而实用的Web文件服务器！这是一个用Go语言编写的轻量级文件管理工具。</p>

        <h2>📋 项目功能</h2>

        <div class="feature">
            <strong>📁 静态文件服务</strong><br>
            自动服务web目录下的所有文件，支持HTML、CSS、JavaScript、图片等各种文件类型。
        </div>

        <div class="feature">
            <strong>📤 文件上传</strong><br>
            <span id="upload-feature-description">文件上传功能可通过命令行参数启用，确保服务器安全。</span>
            <span id="upload-status" class="status-indicator loading">🔄 检查中...</span>
        </div>

        <div class="feature">
            <strong>🌐 WebDAV服务</strong><br>
            <span id="webdav-feature-description">WebDAV服务可通过命令行参数启用，支持文件管理客户端连接。</span>
            <span id="webdav-status" class="status-indicator loading">🔄 检查中...</span>
            <div id="webdav-info" class="webdav-info hidden">
                <strong>WebDAV地址:</strong> <code id="webdav-url">http://localhost:8080/webdav</code><br>
                <strong>挂载目录:</strong> <code id="webdav-directory">.</code><br>
                <strong>访问模式:</strong> <span id="webdav-mode">读写</span>
            </div>
        </div>

        <div class="feature">
            <strong>🔒 HTTPS服务</strong><br>
            <span id="https-feature-description">HTTPS服务可通过命令行参数启用，提供加密的安全连接。</span>
            <span id="https-status" class="status-indicator loading">🔄 检查中...</span>
            <div id="https-info" class="webdav-info hidden">
                <strong>HTTP地址:</strong> <code id="http-url">http://localhost:8080</code><br>
                <strong>HTTPS地址:</strong> <code id="https-url">https://localhost:8443</code><br>
                <strong>证书目录:</strong> <code id="cert-directory">./cert</code><br>
                <strong>证书状态:</strong> <span id="cert-status">检查中</span>
            </div>
        </div>        

        <div class="feature">
            <strong>📂 文件浏览下载</strong><br>
            <span id="files-feature-description">专门的文件浏览页面，展示web目录下的所有文件，支持在线浏览和下载。</span>
            <span id="files-status" class="status-indicator loading">🔄 检查中...</span>
        </div>

        <div class="feature">
            <strong>📊 功能状态展示</strong><br>
            默认页面实时显示服务器各项功能的启用状态，包括文件上传、WebDAV和HTTPS服务。
        </div>

        <div class="feature">
            <strong>🔧 自动配置</strong><br>
            自动创建必要的目录结构，无需手动配置即可使用。
        </div>

        <h2>🚀 快速开始</h2>

        <div id="upload-section">
            <p><strong>文件上传：</strong></p>
            <div id="upload-enabled-content" class="hidden">
                <a href="/upload" class="button" id="upload-button">📤 上传文件</a>
            </div>
            <div id="upload-disabled-content" class="hidden">
                <p>要启用文件上传功能，请使用以下命令启动服务器：</p>
                <code>sweb.exe -upload</code> 或 <code>sweb.exe --enable-upload</code>
                <br><br>
                <a href="/upload" class="button disabled" id="upload-button-disabled">📤 上传功能已禁用</a>
            </div>
        </div>

        <div id="webdav-section">
            <p><strong>WebDAV服务：</strong></p>
            <div id="webdav-enabled-content" class="hidden">
                <a href="/webdav" class="button" id="webdav-button">🌐 访问WebDAV</a>
                <p><small>可以在文件管理器中添加网络位置：<code id="webdav-mount-url">http://localhost:8080/webdav</code></small></p>
            </div>
            <div id="webdav-disabled-content" class="hidden">
                <p>要启用WebDAV服务，请使用以下命令启动服务器：</p>
                <code>sweb.exe -webdav</code> 或 <code>sweb.exe --enable-webdav</code>
                <br><br>
                <a href="/webdav" class="button disabled" id="webdav-button-disabled">🌐 WebDAV服务已禁用</a>
            </div>
        </div>

        <div id="files-section">
            <p><strong>文件浏览：</strong></p>
            <div id="files-enabled-content" class="hidden">
                <a href="/files" class="button" id="files-button">📂 浏览文件</a>
                <p><small>查看和下载web目录中的所有文件</small></p>
            </div>
            <div id="files-disabled-content" class="hidden">
                <p>要启用文件浏览功能，请使用以下命令启动服务器：</p>
                <code>sweb.exe -files</code> 或 <code>sweb.exe --enable-files</code>
                <br><br>
                <a href="/files" class="button disabled" id="files-button-disabled">📂 文件浏览已禁用</a>
            </div>
        </div>

        <p><strong>服务器信息：</strong></p>
        <ul>
            <li>服务端口: <code>8080</code></li>
            <li>文件目录: <code>./web</code></li>
            <li>上传地址: <code>/upload</code></li>
            <li>文件浏览: <code>/files</code></li>
            <li>WebDAV地址: <code>/webdav</code></li>
        </ul>

        <h2>💡 使用说明</h2>
        <div id="usage-instructions">
            <div id="usage-upload-enabled" class="hidden">
                <h3>文件上传</h3>
                <ol>
                    <li><strong>上传文件</strong>：点击上方的"上传文件"按钮，选择要上传的文件</li>
                    <li><strong>访问文件</strong>：上传成功后，文件将保存在web目录下，可以直接通过URL访问</li>
                    <li><strong>管理文件</strong>：所有上传的文件都会显示在主页的文件列表中</li>
                </ol>
            </div>
            <div id="usage-webdav-enabled" class="hidden">
                <h3>WebDAV服务</h3>
                <ol>
                    <li><strong>Windows</strong>：在文件资源管理器中，右键"此电脑" → "映射网络驱动器" → 输入WebDAV地址</li>
                    <li><strong>macOS</strong>：在Finder中，按Cmd+K → 输入WebDAV地址</li>
                    <li><strong>Linux</strong>：使用davfs2或其他WebDAV客户端挂载</li>
                    <li><strong>移动设备</strong>：使用支持WebDAV的文件管理应用</li>
                </ol>
            </div>
            <div id="usage-disabled" class="hidden">
                <ol>
                    <li><strong>启用功能</strong>：使用相应的命令行参数启动服务器</li>
                    <li><strong>浏览文件</strong>：当前可以浏览和下载web目录中的现有文件</li>
                    <li><strong>安全考虑</strong>：高级功能默认禁用，确保服务器安全</li>
                </ol>
            </div>
        </div>

        <h2>🛠️ 技术特性</h2>

        <ul>
            <li>使用Go语言标准库开发，轻量级无外部依赖</li>
            <li>支持多部分表单数据上传</li>
            <li>完整的WebDAV协议支持（RFC 4918）</li>
            <li>可配置的读写权限控制</li>
            <li>自动MIME类型检测</li>
            <li>UTF-8编码支持，完美处理中文</li>
            <li>跨平台兼容（Windows、Linux、macOS）</li>
        </ul>

        <div class="footer">
            <p>🔗 <strong>简单Web文件服务器</strong> | 基于Go语言开发</p>
            <div id="footer-content">
                <div id="footer-enabled" class="hidden">
                    <p>开始使用：
                        <span id="footer-files-link" class="hidden"><a href="/files" class="button">浏览文件</a></span>
                        <span id="footer-upload-link" class="hidden"><a href="/upload" class="button">上传文件</a></span>
                        <span id="footer-webdav-link" class="hidden"><a href="/webdav" class="button">访问WebDAV</a></span>
                    </p>
                </div>
                <div id="footer-disabled" class="hidden">
                    <p>安全模式：高级功能已禁用</p>
                    <p>使用 <code>sweb.exe -help</code> 查看所有可用选项</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查服务状态
        function checkServiceStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    updateUploadStatus(data.upload);
                    updateFilesStatus(data.files);
                    updateWebDAVStatus(data.webdav);
                    updateHTTPSStatus(data.https);
                    updateUsageInstructions(data.upload, data.files, data.webdav, data.https);
                })
                .catch(error => {
                    console.error('检查服务状态失败:', error);
                    // 如果API调用失败，显示默认的禁用状态
                    updateUploadStatus({enabled: false, status: 'disabled'});
                    updateFilesStatus({enabled: false, status: 'disabled'});
                    updateWebDAVStatus({enabled: false, status: 'disabled'});
                    updateHTTPSStatus({enabled: false, status: 'disabled'});
                    updateUsageInstructions({enabled: false}, {enabled: false}, {enabled: false}, {enabled: false});
                });
        }

        // 更新页面上的上传状态显示
        function updateUploadStatus(uploadData) {
            const statusElement = document.getElementById('upload-status');
            const descriptionElement = document.getElementById('upload-feature-description');
            const uploadEnabledContent = document.getElementById('upload-enabled-content');
            const uploadDisabledContent = document.getElementById('upload-disabled-content');

            if (uploadData.enabled) {
                statusElement.textContent = '✅ 已启用';
                statusElement.className = 'status-indicator status-enabled';
                descriptionElement.textContent = '通过简单的Web界面上传文件到服务器，支持各种文件格式。';

                uploadEnabledContent.classList.remove('hidden');
                uploadDisabledContent.classList.add('hidden');
            } else {
                statusElement.textContent = '🔒 已禁用';
                statusElement.className = 'status-indicator status-disabled';
                descriptionElement.textContent = '文件上传功能可通过命令行参数启用，确保服务器安全。';

                uploadEnabledContent.classList.add('hidden');
                uploadDisabledContent.classList.remove('hidden');
            }
        }

        // 更新页面上的文件浏览状态显示
        function updateFilesStatus(filesData) {
            const statusElement = document.getElementById('files-status');
            const descriptionElement = document.getElementById('files-feature-description');
            const filesEnabledContent = document.getElementById('files-enabled-content');
            const filesDisabledContent = document.getElementById('files-disabled-content');

            if (filesData.enabled) {
                statusElement.textContent = '✅ 已启用';
                statusElement.className = 'status-indicator status-enabled';
                descriptionElement.textContent = '专门的文件浏览页面，展示web目录下的所有文件，支持在线浏览和下载。';

                filesEnabledContent.classList.remove('hidden');
                filesDisabledContent.classList.add('hidden');
            } else {
                statusElement.textContent = '🔒 已禁用';
                statusElement.className = 'status-indicator status-disabled';
                descriptionElement.textContent = '文件浏览功能可通过命令行参数启用，确保服务器安全。';

                filesEnabledContent.classList.add('hidden');
                filesDisabledContent.classList.remove('hidden');
            }
        }

        // 更新页面上的WebDAV状态显示
        function updateWebDAVStatus(webdavData) {
            const statusElement = document.getElementById('webdav-status');
            const descriptionElement = document.getElementById('webdav-feature-description');
            const webdavEnabledContent = document.getElementById('webdav-enabled-content');
            const webdavDisabledContent = document.getElementById('webdav-disabled-content');
            const webdavInfo = document.getElementById('webdav-info');
            const webdavDirectory = document.getElementById('webdav-directory');
            const webdavMode = document.getElementById('webdav-mode');

            if (webdavData.enabled) {
                if (webdavData.readonly) {
                    statusElement.textContent = '📖 只读模式';
                    statusElement.className = 'status-indicator status-readonly';
                    descriptionElement.textContent = 'WebDAV服务已启用（只读模式），支持文件浏览和下载。';
                    webdavMode.textContent = '只读';
                } else {
                    statusElement.textContent = '✅ 读写模式';
                    statusElement.className = 'status-indicator status-enabled';
                    descriptionElement.textContent = 'WebDAV服务已启用（读写模式），支持完整的文件管理操作。';
                    webdavMode.textContent = '读写';
                }

                webdavDirectory.textContent = webdavData.directory || '.';
                webdavInfo.classList.remove('hidden');
                webdavEnabledContent.classList.remove('hidden');
                webdavDisabledContent.classList.add('hidden');
            } else {
                statusElement.textContent = '🔒 已禁用';
                statusElement.className = 'status-indicator status-disabled';
                descriptionElement.textContent = 'WebDAV服务可通过命令行参数启用，支持文件管理客户端连接。';

                webdavInfo.classList.add('hidden');
                webdavEnabledContent.classList.add('hidden');
                webdavDisabledContent.classList.remove('hidden');
            }
        }

        // 更新页面上的HTTPS状态显示
        function updateHTTPSStatus(httpsData) {
            const statusElement = document.getElementById('https-status');
            const descriptionElement = document.getElementById('https-feature-description');
            const httpsInfo = document.getElementById('https-info');
            const httpUrl = document.getElementById('http-url');
            const httpsUrl = document.getElementById('https-url');
            const certDirectory = document.getElementById('cert-directory');
            const certStatus = document.getElementById('cert-status');

            if (httpsData.enabled) {
                if (httpsData.certStatus === 'enabled') {
                    statusElement.textContent = '✅ 已启用';
                    statusElement.className = 'status-indicator status-enabled';
                    descriptionElement.textContent = 'HTTPS服务已启用，提供加密的安全连接。';
                    certStatus.textContent = '证书正常';
                    certStatus.className = 'status-enabled';
                } else if (httpsData.certStatus === 'cert-error') {
                    statusElement.textContent = '⚠️ 证书错误';
                    statusElement.className = 'status-indicator status-readonly';
                    descriptionElement.textContent = 'HTTPS服务已启用，但证书文件有问题。';
                    certStatus.textContent = '证书错误';
                    certStatus.className = 'status-disabled';
                }

                httpUrl.textContent = 'http://localhost:' + httpsData.httpPort;
                httpsUrl.textContent = 'https://localhost:' + httpsData.httpsPort;
                certDirectory.textContent = httpsData.certDir || './cert';
                httpsInfo.classList.remove('hidden');
            } else {
                statusElement.textContent = '🔒 已禁用';
                statusElement.className = 'status-indicator status-disabled';
                descriptionElement.textContent = 'HTTPS服务可通过命令行参数启用，提供加密的安全连接。';

                httpsInfo.classList.add('hidden');
            }
        }

        // 更新使用说明和页脚
        function updateUsageInstructions(uploadData, filesData, webdavData, httpsData) {
            const usageUploadEnabled = document.getElementById('usage-upload-enabled');
            const usageWebdavEnabled = document.getElementById('usage-webdav-enabled');
            const usageDisabled = document.getElementById('usage-disabled');
            const footerEnabled = document.getElementById('footer-enabled');
            const footerDisabled = document.getElementById('footer-disabled');
            const footerFilesLink = document.getElementById('footer-files-link');
            const footerUploadLink = document.getElementById('footer-upload-link');
            const footerWebdavLink = document.getElementById('footer-webdav-link');

            const anyEnabled = uploadData.enabled || filesData.enabled || webdavData.enabled || httpsData.enabled;

            if (anyEnabled) {
                footerEnabled.classList.remove('hidden');
                footerDisabled.classList.add('hidden');
                usageDisabled.classList.add('hidden');

                if (filesData.enabled) {
                    footerFilesLink.classList.remove('hidden');
                } else {
                    footerFilesLink.classList.add('hidden');
                }

                if (uploadData.enabled) {
                    usageUploadEnabled.classList.remove('hidden');
                    footerUploadLink.classList.remove('hidden');
                } else {
                    usageUploadEnabled.classList.add('hidden');
                    footerUploadLink.classList.add('hidden');
                }

                if (webdavData.enabled) {
                    usageWebdavEnabled.classList.remove('hidden');
                    footerWebdavLink.classList.remove('hidden');
                } else {
                    usageWebdavEnabled.classList.add('hidden');
                    footerWebdavLink.classList.add('hidden');
                }
            } else {
                footerEnabled.classList.add('hidden');
                footerDisabled.classList.remove('hidden');
                usageDisabled.classList.remove('hidden');
                usageUploadEnabled.classList.add('hidden');
                usageWebdavEnabled.classList.add('hidden');
            }
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();

            // 每30秒检查一次状态
            setInterval(checkServiceStatus, 30000);
        });
    </script>
</body>
</html>`
}
