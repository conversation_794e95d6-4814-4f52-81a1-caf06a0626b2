@echo off
chcp 65001 >nul
echo ========================================
echo Complete Proxy Test Suite
echo 完整代理测试套件
echo ========================================
echo.

echo Please ensure the server is started with all proxy features:
echo 请确保服务器已启动并启用了所有代理功能：
echo ./sweb.exe -socks5 -proxy
echo.

echo Starting comprehensive proxy tests...
echo 开始综合代理测试...
echo.

echo ========================================
echo 1. SOCKS5 Proxy Test
echo 1. SOCKS5代理测试
echo ========================================
echo.

echo Compiling SOCKS5 test...
echo 编译SOCKS5测试...
go build -o test_socks5_exe.exe test_socks5.go
if %errorlevel% neq 0 (
    echo X SOCKS5 test compilation failed
    echo X SOCKS5测试编译失败
    goto :http_test
)

echo Running SOCKS5 test...
echo 运行SOCKS5测试...
test_socks5_exe.exe
echo.

:http_test
echo ========================================
echo 2. HTTP/HTTPS Proxy Test
echo 2. HTTP/HTTPS代理测试
echo ========================================
echo.

echo Compiling HTTP proxy test...
echo 编译HTTP代理测试...
go build -o test_proxy_exe.exe test_proxy.go
if %errorlevel% neq 0 (
    echo X HTTP proxy test compilation failed
    echo X HTTP代理测试编译失败
    goto :debug_test
)

echo Running HTTP/HTTPS proxy test...
echo 运行HTTP/HTTPS代理测试...
test_proxy_exe.exe
echo.

:debug_test
echo ========================================
echo 3. Debug Connection Test
echo 3. 调试连接测试
echo ========================================
echo.

echo Compiling debug test...
echo 编译调试测试...
go build -o debug_socks5_exe.exe debug_socks5.go
if %errorlevel% neq 0 (
    echo X Debug test compilation failed
    echo X 调试测试编译失败
    goto :cleanup
)

echo Running debug connection test...
echo 运行调试连接测试...
debug_socks5_exe.exe
echo.

:cleanup
echo ========================================
echo Cleaning up temporary files...
echo 清理临时文件...
echo ========================================
if exist test_socks5_exe.exe del test_socks5_exe.exe
if exist test_proxy_exe.exe del test_proxy_exe.exe
if exist debug_socks5_exe.exe del debug_socks5_exe.exe

echo.
echo ========================================
echo All proxy tests completed!
echo 所有代理测试完成！
echo ========================================
echo.
echo Summary / 总结:
echo - SOCKS5 proxy: localhost:1080
echo - SOCKS5代理: localhost:1080
echo - HTTP proxy: localhost:10808
echo - HTTP代理: localhost:10808
echo - HTTPS proxy: localhost:10808 (same as HTTP)
echo - HTTPS代理: localhost:10808 (与HTTP相同)
echo.
echo Browser configuration tips:
echo 浏览器配置提示：
echo 1. For SOCKS5: Set SOCKS5 proxy to 127.0.0.1:1080
echo 1. SOCKS5: 设置SOCKS5代理为 127.0.0.1:1080
echo 2. For HTTP/HTTPS: Set HTTP proxy to 127.0.0.1:10808
echo 2. HTTP/HTTPS: 设置HTTP代理为 127.0.0.1:10808
echo.
pause
