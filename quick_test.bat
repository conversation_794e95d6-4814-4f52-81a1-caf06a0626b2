@echo off
chcp 65001 >nul
echo ========================================
echo SOCKS5 Proxy Quick Test
echo SOCKS5代理快速测试
echo ========================================
echo.

echo 1. Compiling program...
echo 1. 编译程序...
go build -o sweb.exe main.go upload.go files.go webdav.go server.go utils.go socks5.go proxy.go
if %errorlevel% neq 0 (
    echo X Compilation failed
    echo X 编译失败
    pause
    exit /b 1
)
echo √ Compilation successful
echo √ 编译成功

echo.
echo 2. Starting server...
echo 2. 启动服务器...
echo Please run in another command window: sweb.exe -socks5
echo 请在另一个命令行窗口运行: sweb.exe -socks5
echo Then press any key to continue testing...
echo 然后按任意键继续测试...
pause

echo.
echo 3. Testing SOCKS5 connection...
echo 3. 测试SOCKS5连接...
go run debug_socks5.go

echo.
echo ========================================
echo Test completed!
echo 测试完成！
echo ========================================
echo.
echo If test successful, please reconfigure browser:
echo 如果测试成功，请重新配置浏览器：
echo 1. Make sure to select SOCKS5 proxy (not HTTP proxy)
echo 1. 确保选择SOCKS5代理（不是HTTP代理）
echo 2. Address: 127.0.0.1
echo 2. 地址: 127.0.0.1
echo 3. Port: 1080
echo 3. 端口: 1080
echo.
pause
